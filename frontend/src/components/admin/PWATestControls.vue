<template>
  <div class="bg-base-100 rounded-lg shadow-lg p-4 md:p-6">
    <div class="flex items-center justify-between mb-4 md:mb-6">
      <h2 class="text-lg md:text-2xl font-bold flex items-center">
        <Icon name="smartphone" size="md" class="mr-2 md:mr-3 text-primary" />
        PWA Update Prompt Test
      </h2>
    </div>

    <!-- PWA Status -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div class="stat bg-base-200 rounded-lg p-4">
        <div class="stat-title text-xs">PWA Status</div>
        <div class="stat-value text-sm md:text-lg" :class="{
          'text-success': isInstalled,
          'text-warning': !isInstalled
        }">
          {{ isInstalled ? 'Installed' : 'Not Installed' }}
        </div>
      </div>
      
      <div class="stat bg-base-200 rounded-lg p-4">
        <div class="stat-title text-xs">Service Worker</div>
        <div class="stat-value text-sm md:text-lg" :class="{
          'text-success': hasServiceWorker,
          'text-error': !hasServiceWorker
        }">
          {{ hasServiceWorker ? 'Active' : 'Inactive' }}
        </div>
      </div>
      
      <div class="stat bg-base-200 rounded-lg p-4">
        <div class="stat-title text-xs">Update Available</div>
        <div class="stat-value text-sm md:text-lg" :class="{
          'text-info': needRefresh,
          'text-neutral': !needRefresh
        }">
          {{ needRefresh ? 'Yes' : 'No' }}
        </div>
      </div>
    </div>

    <!-- Test Controls -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
      <!-- Update Prompt Tests -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-sm md:text-lg font-semibold mb-4 flex items-center">
          <Icon name="refresh" size="sm" class="mr-2 text-primary" />
          Update Prompt Tests
        </h3>
        
        <div class="space-y-3">
          <button 
            @click="simulateUpdateAvailable"
            class="btn btn-primary btn-sm w-full"
          >
            <Icon name="download" size="sm" class="mr-2" />
            Simulate Update Available
          </button>
          
          <button 
            @click="simulateOfflineReady"
            class="btn btn-success btn-sm w-full"
          >
            <Icon name="check-circle" size="sm" class="mr-2" />
            Simulate Offline Ready
          </button>
          
          <button 
            @click="simulateCriticalUpdate"
            class="btn btn-warning btn-sm w-full"
          >
            <Icon name="alert-triangle" size="sm" class="mr-2" />
            Simulate Critical Update
          </button>
        </div>
      </div>

      <!-- PWA Installation -->
      <div class="bg-base-200 rounded-lg p-4">
        <h3 class="text-sm md:text-lg font-semibold mb-4 flex items-center">
          <Icon name="smartphone" size="sm" class="mr-2 text-secondary" />
          PWA Installation
        </h3>
        
        <div class="space-y-3">
          <button
            v-if="!isInstalled && (canInstall || showInstallPrompt)"
            @click="installPWA"
            class="btn btn-secondary btn-sm w-full"
          >
            <Icon name="download" size="sm" class="mr-2" />
            Install PWA
          </button>
          
          <button 
            @click="checkPWAStatus"
            class="btn btn-info btn-sm w-full"
          >
            <Icon name="refresh" size="sm" class="mr-2" />
            Check PWA Status
          </button>
          
          <button 
            @click="clearPWAData"
            class="btn btn-error btn-sm w-full"
          >
            <Icon name="trash" size="sm" class="mr-2" />
            Clear PWA Data
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Responsiveness Test -->
    <div class="mt-6 bg-base-200 rounded-lg p-4">
      <h3 class="text-sm md:text-lg font-semibold mb-4 flex items-center">
        <Icon name="smartphone" size="sm" class="mr-2 text-accent" />
        Mobile Responsiveness Test
      </h3>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
        <button 
          @click="testMobileView('320px')"
          class="btn btn-outline btn-sm"
        >
          📱 Mobile S (320px)
        </button>
        
        <button 
          @click="testMobileView('375px')"
          class="btn btn-outline btn-sm"
        >
          📱 Mobile M (375px)
        </button>
        
        <button 
          @click="testMobileView('425px')"
          class="btn btn-outline btn-sm"
        >
          📱 Mobile L (425px)
        </button>
        
        <button 
          @click="testMobileView('768px')"
          class="btn btn-outline btn-sm"
        >
          📱 Tablet (768px)
        </button>
      </div>
      
      <div class="mt-4 text-xs md:text-sm text-base-content/70">
        Current viewport: {{ currentViewport }}
      </div>
    </div>

    <!-- Event Log -->
    <div class="mt-6 bg-base-200 rounded-lg p-4">
      <h3 class="text-sm md:text-lg font-semibold mb-4 flex items-center">
        <Icon name="list" size="sm" class="mr-2 text-neutral" />
        PWA Event Log
        <button 
          @click="clearLog"
          class="btn btn-ghost btn-xs ml-auto"
        >
          Clear
        </button>
      </h3>
      
      <div class="bg-base-100 rounded p-3 max-h-40 md:max-h-60 overflow-y-auto">
        <div 
          v-for="(event, index) in eventLog" 
          :key="index"
          class="text-xs md:text-sm font-mono mb-2 p-2 rounded"
          :class="{
            'bg-success/20 text-success': event.type === 'success',
            'bg-error/20 text-error': event.type === 'error',
            'bg-info/20 text-info': event.type === 'info',
            'bg-warning/20 text-warning': event.type === 'warning'
          }"
        >
          <div class="flex justify-between items-start">
            <span class="font-semibold">{{ event.title }}</span>
            <span class="text-xs opacity-70">{{ formatTime(event.timestamp) }}</span>
          </div>
          <div class="mt-1 opacity-80 break-words">{{ event.message }}</div>
        </div>
        
        <div v-if="eventLog.length === 0" class="text-center py-4 text-base-content/60">
          No PWA events yet. Try the buttons above!
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { usePWA } from '@/composables/usePWA'
import Icon from '@/components/common/Icon.vue'

// PWA composable
const {
  needRefresh,
  offlineReady,
  updateServiceWorker,
  isInstalled,
  isStandalone,
  showInstallPrompt,
  handleInstallPrompt,
  forceShowInstallPrompt
} = usePWA()

// Local state
const eventLog = ref<any[]>([])
const hasServiceWorker = ref(false)
const canInstall = ref(false)
const currentViewport = ref('')

// Computed
const updateViewport = () => {
  currentViewport.value = `${window.innerWidth}x${window.innerHeight}`
}

// Helper functions
const addToLog = (type: string, title: string, message: string) => {
  eventLog.value.unshift({
    type,
    title,
    message,
    timestamp: new Date().toISOString()
  })
  
  // Keep only last 20 events
  if (eventLog.value.length > 20) {
    eventLog.value = eventLog.value.slice(0, 20)
  }
}

const clearLog = () => {
  eventLog.value = []
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString()
}

// PWA test functions
const simulateUpdateAvailable = () => {
  // Trigger update prompt manually
  needRefresh.value = true
  addToLog('info', 'Update Simulated', 'Simulated update available prompt')
}

const simulateOfflineReady = () => {
  // Trigger offline ready notification
  offlineReady.value = true
  addToLog('success', 'Offline Ready', 'Simulated offline ready notification')
}

const simulateCriticalUpdate = () => {
  // This would trigger the critical update modal
  addToLog('warning', 'Critical Update', 'Simulated critical update prompt')
  // You can emit an event or call a method to show critical update
}

const installPWA = async () => {
  try {
    addToLog('info', 'PWA Install', 'Attempting PWA installation...')
    const result = await handleInstallPrompt()
    if (result) {
      addToLog('success', 'PWA Installed', 'PWA installation successful')
    } else {
      addToLog('warning', 'Install Cancelled', 'User cancelled PWA installation')
    }
  } catch (error: any) {
    addToLog('error', 'Install Failed', error.message)
  }
}

const checkPWAStatus = () => {
  // Check if running as PWA
  const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches
  const isInWebAppiOS = (window.navigator as any).standalone === true

  hasServiceWorker.value = 'serviceWorker' in navigator

  addToLog('info', 'PWA Status', `PWA: ${isInstalled.value ? 'Installed' : 'Not Installed'}, SW: ${hasServiceWorker.value ? 'Active' : 'Inactive'}`)
  addToLog('info', 'Display Mode', `Standalone: ${isStandaloneMode}, iOS: ${isInWebAppiOS}`)
}

const clearPWAData = async () => {
  try {
    // Clear service worker cache
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations()
      for (const registration of registrations) {
        await registration.unregister()
      }
    }
    
    // Clear caches
    if ('caches' in window) {
      const cacheNames = await caches.keys()
      await Promise.all(cacheNames.map(name => caches.delete(name)))
    }
    
    addToLog('success', 'PWA Data Cleared', 'Service worker and caches cleared')
  } catch (error: any) {
    addToLog('error', 'Clear Failed', error.message)
  }
}

const testMobileView = (width: string) => {
  addToLog('info', 'Mobile Test', `Testing mobile view at ${width}`)
  // This is just for demonstration - in a real app you might open dev tools or provide instructions
  alert(`To test mobile view:\n1. Open browser dev tools (F12)\n2. Click device toolbar icon\n3. Set width to ${width}\n4. Test the PWA update prompts`)
}

// Store event handlers for cleanup
const resizeHandler = updateViewport
const installPromptHandler = (e: Event) => {
  canInstall.value = true
  addToLog('info', 'Install Prompt', 'PWA installation prompt available')
}
const controllerChangeHandler = () => {
  addToLog('success', 'SW Updated', 'Service worker updated')
}

// Initialize
onMounted(() => {
  checkPWAStatus()
  updateViewport()

  // Listen for viewport changes
  window.addEventListener('resize', resizeHandler)

  // Listen for PWA events
  window.addEventListener('beforeinstallprompt', installPromptHandler)

  // Check for service worker updates
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('controllerchange', controllerChangeHandler)
  }
})

// CRITICAL: Clean up all event listeners on unmount
onUnmounted(() => {
  console.log('🧹 Cleaning up PWATestControls event listeners...')

  // Remove window event listeners
  window.removeEventListener('resize', resizeHandler)
  window.removeEventListener('beforeinstallprompt', installPromptHandler)

  // Remove service worker event listeners
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.removeEventListener('controllerchange', controllerChangeHandler)
  }

  console.log('✅ All PWATestControls event listeners removed')
})
</script>
