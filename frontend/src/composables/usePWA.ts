import { ref, onMounted, onUnmounted } from 'vue'
import { useRegisterSW } from 'virtual:pwa-register/vue'

export interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[]
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed'
    platform: string
  }>
  prompt(): Promise<void>
}

export function usePWA() {
  // PWA Installation
  const deferredPrompt = ref<BeforeInstallPromptEvent | null>(null)
  const showInstallPrompt = ref(false)
  const isInstalled = ref(false)
  const isStandalone = ref(false)

  // Service Worker
  const {
    needRefresh,
    offlineReady,
    updateServiceWorker,
  } = useRegisterSW({
    onRegistered(r) {
      console.log('SW Registered: ' + r)
    },
    onRegisterError(error) {
      console.log('SW registration error', error)
    },
  })

  // Network Status
  const isOnline = ref(navigator.onLine)
  const networkStatus = ref<'online' | 'offline' | 'slow'>('online')

  // Check if app is installed
  const checkInstallStatus = () => {
    // Check if running in standalone mode
    isStandalone.value = window.matchMedia('(display-mode: standalone)').matches ||
                        (window.navigator as any).standalone === true

    // Check if app is installed (various methods)
    isInstalled.value = isStandalone.value ||
                       document.referrer.includes('android-app://') ||
                       (window as any).chrome?.app?.isInstalled

    return isInstalled.value
  }

  // Handle install prompt
  const handleInstallPrompt = async () => {
    if (!deferredPrompt.value) {
      console.log('No deferred prompt available')
      return false
    }

    try {
      console.log('Triggering install prompt')
      // Show the install prompt
      await deferredPrompt.value.prompt()

      // Wait for the user to respond to the prompt
      const { outcome } = await deferredPrompt.value.userChoice

      if (outcome === 'accepted') {
        console.log('User accepted the install prompt')
        showInstallPrompt.value = false
        deferredPrompt.value = null
        return true
      } else {
        console.log('User dismissed the install prompt')
        return false
      }
    } catch (error) {
      console.error('Error showing install prompt:', error)
      return false
    }
  }

  // Force show install prompt (for testing)
  const forceShowInstallPrompt = () => {
    if (!isInstalled.value) {
      console.log('Forcing install prompt to show')
      showInstallPrompt.value = true
    }
  }

  // Dismiss install prompt
  const dismissInstallPrompt = () => {
    showInstallPrompt.value = false
    localStorage.setItem('pwa-install-dismissed', Date.now().toString())
  }

  // Check if install prompt was recently dismissed
  const wasRecentlyDismissed = () => {
    const dismissed = localStorage.getItem('pwa-install-dismissed')
    if (!dismissed) return false
    
    const dismissedTime = parseInt(dismissed)
    const dayInMs = 24 * 60 * 60 * 1000
    return (Date.now() - dismissedTime) < (7 * dayInMs) // 7 days
  }

  // Update network status
  const updateNetworkStatus = () => {
    isOnline.value = navigator.onLine
    
    if (!navigator.onLine) {
      networkStatus.value = 'offline'
    } else {
      // Check connection speed (simplified)
      const connection = (navigator as any).connection
      if (connection) {
        const slowConnections = ['slow-2g', '2g', '3g']
        networkStatus.value = slowConnections.includes(connection.effectiveType) ? 'slow' : 'online'
      } else {
        networkStatus.value = 'online'
      }
    }
  }

  // Initialize PWA features
  const initializePWA = () => {
    // Check install status
    checkInstallStatus()

    // Listen for beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e: Event) => {
      console.log('beforeinstallprompt event fired')
      e.preventDefault()
      deferredPrompt.value = e as BeforeInstallPromptEvent

      // Show install prompt if not recently dismissed and not already installed
      if (!isInstalled.value && !wasRecentlyDismissed()) {
        console.log('Showing PWA install prompt')
        showInstallPrompt.value = true
      } else {
        console.log('PWA install prompt not shown:', {
          isInstalled: isInstalled.value,
          wasRecentlyDismissed: wasRecentlyDismissed()
        })
      }
    })

    // Listen for app installed event
    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed')
      isInstalled.value = true
      showInstallPrompt.value = false
      deferredPrompt.value = null
    })

    // Listen for network changes
    window.addEventListener('online', updateNetworkStatus)
    window.addEventListener('offline', updateNetworkStatus)
    
    // Initial network status check
    updateNetworkStatus()

    // Listen for display mode changes
    const mediaQuery = window.matchMedia('(display-mode: standalone)')
    mediaQuery.addEventListener('change', checkInstallStatus)
    eventListeners.set(mediaQuery, checkInstallStatus)
  }

  // Store event listeners for proper cleanup
  const eventListeners = new Map()

  // Enhanced cleanup to prevent memory leaks
  const cleanup = () => {
    window.removeEventListener('online', updateNetworkStatus)
    window.removeEventListener('offline', updateNetworkStatus)

    // Clean up all stored event listeners
    eventListeners.forEach((listener, element) => {
      if (element && typeof element.removeEventListener === 'function') {
        element.removeEventListener('change', listener)
      }
    })
    eventListeners.clear()
  }

  // Auto-initialize on mount
  onMounted(() => {
    initializePWA()
  })

  // Clean up on unmount to prevent memory leaks
  onUnmounted(() => {
    cleanup()
  })

  return {
    // Installation
    showInstallPrompt,
    isInstalled,
    isStandalone,
    handleInstallPrompt,
    dismissInstallPrompt,
    forceShowInstallPrompt,

    // Service Worker
    needRefresh,
    offlineReady,
    updateServiceWorker,

    // Network
    isOnline,
    networkStatus,

    // Methods
    initializePWA,
    cleanup,
  }
}
